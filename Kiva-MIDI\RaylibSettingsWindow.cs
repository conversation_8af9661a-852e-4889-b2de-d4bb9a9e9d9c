using System;
using System.Collections.Generic;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based settings window (recreates the WPF SettingsWindow)
    /// </summary>
    public class RaylibSettingsWindow : IDisposable
    {
        private bool isOpen = false;
        private int windowWidth = 608;
        private int windowHeight = 400;
        private Settings settings;
        private List<RaylibUIElement> uiElements;
        private RaylibUIElement hoveredElement;
        private RaylibUIElement pressedElement;
        private int currentTab = 0; // 0=Visual, 1=Audio, 2=Misc, 3=Advanced
        
        // UI Colors (matching original Kiva theme)
        private readonly RaylibColor BarColor = new RaylibColor(0, 104, 201, 255); // Blue
        private readonly RaylibColor ContentColor = new RaylibColor(1, 121, 219, 255); // Lighter blue
        private readonly RaylibColor WhiteColor = new RaylibColor(255, 255, 255, 255);
        private readonly RaylibColor BlackColor = new RaylibColor(0, 0, 0, 255);
        private readonly RaylibColor GrayColor = new RaylibColor(128, 128, 128, 255);
        private readonly RaylibColor LightGrayColor = new RaylibColor(200, 200, 200, 255);
        private readonly RaylibColor RedColor = new RaylibColor(255, 0, 0, 255);
        
        // Layout constants
        private const int TitleBarHeight = 40;
        private const int TabButtonWidth = 87;
        private const int TabButtonHeight = 40;
        private const int Margin = 10;
        private const int ControlHeight = 30;
        private const int SliderWidth = 200;
        
        public RaylibSettingsWindow(Settings settings)
        {
            this.settings = settings;
            this.uiElements = new List<RaylibUIElement>();
        }
        
        public void Open()
        {
            if (isOpen) return;
            
            isOpen = true;
            Raylib.InitWindow(windowWidth, windowHeight, "Kiva Settings");
            Raylib.SetWindowPosition(
                (Raylib.GetMonitorWidth(0) - windowWidth) / 2,
                (Raylib.GetMonitorHeight(0) - windowHeight) / 2
            );
            
            CreateUIElements();
        }
        
        public void Close()
        {
            if (!isOpen) return;
            
            isOpen = false;
            Raylib.CloseWindow();
        }
        
        public bool IsOpen => isOpen;
        
        public void Update()
        {
            if (!isOpen) return;
            
            if (Raylib.WindowShouldClose())
            {
                Close();
                return;
            }
            
            HandleInput();
            Render();
        }
        
        private void CreateUIElements()
        {
            uiElements.Clear();
            
            // Title bar background
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Background,
                Bounds = new Rectangle(0, 0, windowWidth, TitleBarHeight),
                Color = BarColor,
                Name = "TitleBar"
            });
            
            // Tab buttons
            string[] tabNames = { "Visual", "Audio", "Misc", "Advanced" };
            for (int i = 0; i < tabNames.Length; i++)
            {
                int tabWidth = i == 3 ? 100 : TabButtonWidth; // Advanced tab is wider
                uiElements.Add(new RaylibUIElement
                {
                    Type = UIElementType.Button,
                    Bounds = new Rectangle(i * TabButtonWidth, 0, tabWidth, TabButtonHeight),
                    Color = currentTab == i ? ContentColor : BarColor,
                    Name = $"Tab{i}",
                    Text = tabNames[i],
                    Action = () => SwitchTab(i)
                });
            }
            
            // Close button
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(windowWidth - 30, 5, 20, 20),
                Color = RedColor,
                Name = "CloseButton",
                Text = "×",
                Action = () => Close()
            });

            // Content area background
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Background,
                Bounds = new Rectangle(0, TitleBarHeight, windowWidth, windowHeight - TitleBarHeight),
                Color = ContentColor,
                Name = "ContentArea"
            });
            
            // Create tab-specific content
            CreateTabContent();
        }
        
        private void CreateTabContent()
        {
            // Remove existing tab content
            uiElements.RemoveAll(e => e.Name.StartsWith("Content_"));
            
            int y = TitleBarHeight + Margin;
            
            switch (currentTab)
            {
                case 0: // Visual
                    CreateVisualTab(ref y);
                    break;
                case 1: // Audio
                    CreateAudioTab(ref y);
                    break;
                case 2: // Misc
                    CreateMiscTab(ref y);
                    break;
                case 3: // Advanced
                    CreateAdvancedTab(ref y);
                    break;
            }
        }
        
        private void CreateVisualTab(ref int y)
        {
            // Keyboard Style
            AddLabel("Keyboard Style:", Margin, y);
            y += 25;
            AddDropdown("Content_KeyboardStyle", Margin, y, 200, new[] { "None", "Small", "Big" },
                (int)settings.General.KeyboardStyle);
            y += ControlHeight + Margin;

            // Note Speed (use default value since not in settings)
            AddLabel("Note Speed:", Margin, y);
            y += 25;
            AddSlider("Content_NoteSpeed", Margin, y, SliderWidth, 0.1f, 5.0f, 1.0f);
            y += ControlHeight + Margin;

            // Max Polyphony (use render voices)
            AddLabel("Max Polyphony:", Margin, y);
            y += 25;
            AddSlider("Content_MaxPolyphony", Margin, y, SliderWidth, 1, 2000, settings.General.RenderVoices);
            y += ControlHeight + Margin;

            // First Note
            AddLabel("First Note:", Margin, y);
            y += 25;
            AddSlider("Content_FirstNote", Margin, y, SliderWidth, 0, 127, settings.General.CustomFirstKey);
            y += ControlHeight + Margin;

            // Last Note
            AddLabel("Last Note:", Margin, y);
            y += 25;
            AddSlider("Content_LastNote", Margin, y, SliderWidth, 0, 127, settings.General.CustomLastKey);
        }
        
        private void CreateAudioTab(ref int y)
        {
            // Audio Engine
            AddLabel("Audio Engine:", Margin, y);
            y += 25;
            AddDropdown("Content_AudioEngine", Margin, y, 200, new[] { "KDMAPI", "WinMM", "PreRender" },
                (int)settings.General.SelectedAudioEngine);
            y += ControlHeight + Margin * 2;

            // Volume (use default since not in settings)
            AddLabel("Volume:", Margin, y);
            y += 25;
            AddSlider("Content_Volume", Margin, y, SliderWidth, 0.0f, 1.0f, 1.0f);
            y += ControlHeight + Margin;

            // Audio Buffer (use render buffer length)
            AddLabel("Audio Buffer:", Margin, y);
            y += 25;
            AddSlider("Content_AudioBuffer", Margin, y, SliderWidth, 1, 100, settings.General.RenderBufferLength);
        }
        
        private void CreateMiscTab(ref int y)
        {
            // Window settings
            AddLabel("Window Settings:", Margin, y);
            y += 25;
            AddCheckbox("Content_Fullscreen", "Fullscreen", Margin, y, false); // Default false
            y += ControlHeight;
            AddCheckbox("Content_Topmost", "Always on Top", Margin, y, settings.General.MainWindowTopmost);
            y += ControlHeight + Margin;

            // Performance
            AddLabel("Performance:", Margin, y);
            y += 25;
            AddCheckbox("Content_VSync", "VSync", Margin, y, settings.General.SyncFPS);
            y += ControlHeight;
            AddSlider("Content_FPSLimit", Margin, y, SliderWidth, 30, 240, settings.General.FPSLock);
        }
        
        private void CreateAdvancedTab(ref int y)
        {
            // Advanced settings
            AddLabel("Advanced Settings:", Margin, y);
            y += 25;
            AddCheckbox("Content_DebugMode", "Debug Mode", Margin, y, false);
            y += ControlHeight;
            AddCheckbox("Content_ShowStats", "Show Statistics", Margin, y, false);
            y += ControlHeight + Margin;
            
            // Reset button
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(Margin, y, 120, ControlHeight),
                Color = RedColor,
                Name = "Content_ResetButton",
                Text = "Reset Settings",
                Action = () => ResetSettings()
            });
        }
        
        private void AddLabel(string text, int x, int y)
        {
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Label,
                Bounds = new Rectangle(x, y, 200, 20),
                Color = WhiteColor,
                Name = $"Content_Label_{text}",
                Text = text
            });
        }

        private void AddSlider(string name, int x, int y, int width, float min, float max, float value)
        {
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Slider,
                Bounds = new Rectangle(x, y, width, 20),
                Color = BlackColor,
                Name = name,
                Value = value,
                MinValue = min,
                MaxValue = max,
                Action = () => OnSliderChanged(name)
            });
        }
        
        private void AddCheckbox(string name, string text, int x, int y, bool value)
        {
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Button, // Use button for checkbox
                Bounds = new Rectangle(x, y, 20, 20),
                Color = value ? WhiteColor : GrayColor,
                Name = name,
                Text = value ? "✓" : "",
                Action = () => OnCheckboxChanged(name)
            });

            // Checkbox label
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Label,
                Bounds = new Rectangle(x + 25, y, 200, 20),
                Color = WhiteColor,
                Name = $"{name}_Label",
                Text = text
            });
        }

        private void AddDropdown(string name, int x, int y, int width, string[] options, int selectedIndex)
        {
            // For now, implement as a button that cycles through options
            uiElements.Add(new RaylibUIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(x, y, width, ControlHeight),
                Color = WhiteColor,
                Name = name,
                Text = options[selectedIndex],
                Value = selectedIndex,
                MaxValue = options.Length - 1,
                Action = () => OnDropdownChanged(name, options)
            });
        }
        
        private void SwitchTab(int tabIndex)
        {
            currentTab = tabIndex;
            CreateUIElements();
        }
        
        private void OnSliderChanged(string name)
        {
            var slider = GetElement(name);
            if (slider == null) return;
            
            // Update settings based on slider name
            // Implementation depends on specific setting
        }
        
        private void OnCheckboxChanged(string name)
        {
            var checkbox = GetElement(name);
            if (checkbox == null) return;
            
            // Toggle checkbox state
            bool newState = string.IsNullOrEmpty(checkbox.Text);
            checkbox.Text = newState ? "✓" : "";
            checkbox.Color = newState ? WhiteColor : GrayColor;
        }
        
        private void OnDropdownChanged(string name, string[] options)
        {
            var dropdown = GetElement(name);
            if (dropdown == null) return;
            
            // Cycle to next option
            int newIndex = ((int)dropdown.Value + 1) % options.Length;
            dropdown.Value = newIndex;
            dropdown.Text = options[newIndex];
        }
        
        private void ResetSettings()
        {
            // Reset settings to defaults
            // Implementation depends on Settings class
        }
        
        private RaylibUIElement GetElement(string name)
        {
            return uiElements.Find(e => e.Name == name);
        }

        private void HandleInput()
        {
            Vector2 mousePos = Raylib.GetMousePosition();
            bool mousePressed = Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT);
            bool mouseDown = Raylib.IsMouseButtonDown(MOUSE_BUTTON_LEFT);
            bool mouseReleased = Raylib.IsMouseButtonReleased(MOUSE_BUTTON_LEFT);

            // Find hovered element
            RaylibUIElement newHovered = null;
            foreach (var element in uiElements)
            {
                if (element.Type != UIElementType.Background &&
                    IsPointInRectangle(mousePos, element.Bounds))
                {
                    newHovered = element;
                    break;
                }
            }

            hoveredElement = newHovered;

            // Handle mouse press
            if (mousePressed && hoveredElement != null)
            {
                pressedElement = hoveredElement;
            }

            // Handle mouse release
            if (mouseReleased && pressedElement != null)
            {
                if (pressedElement == hoveredElement)
                {
                    pressedElement.Action?.Invoke();
                }
                pressedElement = null;
            }

            // Handle slider dragging
            if (mouseDown && pressedElement?.Type == UIElementType.Slider)
            {
                UpdateSliderValue(pressedElement, mousePos);
            }
        }

        private void UpdateSliderValue(RaylibUIElement slider, Vector2 mousePos)
        {
            float relativeX = (mousePos.X - slider.Bounds.x) / slider.Bounds.width;
            relativeX = Math.Max(0, Math.Min(1, relativeX));

            slider.Value = slider.MinValue + relativeX * (slider.MaxValue - slider.MinValue);
            slider.Action?.Invoke();
        }

        private bool IsPointInRectangle(Vector2 point, Rectangle rect)
        {
            return point.X >= rect.x && point.X <= rect.x + rect.width &&
                   point.Y >= rect.y && point.Y <= rect.y + rect.height;
        }

        private void Render()
        {
            Raylib.BeginDrawing();
            Raylib.ClearBackground(ContentColor);

            foreach (var element in uiElements)
            {
                RenderElement(element);
            }

            Raylib.EndDrawing();
        }

        private void RenderElement(RaylibUIElement element)
        {
            switch (element.Type)
            {
                case UIElementType.Background:
                    RenderBackground(element);
                    break;
                case UIElementType.Button:
                    RenderButton(element);
                    break;
                case UIElementType.Slider:
                    RenderSlider(element);
                    break;
                case UIElementType.Label:
                    RenderLabel(element);
                    break;
            }
        }

        private void RenderBackground(RaylibUIElement element)
        {
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, element.Color);
        }

        private void RenderButton(RaylibUIElement element)
        {
            RaylibColor buttonColor = element.Color;

            // Hover effect
            if (hoveredElement == element)
            {
                buttonColor = new RaylibColor(
                    (byte)Math.Min(255, buttonColor.r * 1.2f),
                    (byte)Math.Min(255, buttonColor.g * 1.2f),
                    (byte)Math.Min(255, buttonColor.b * 1.2f),
                    buttonColor.a
                );
            }

            // Press effect
            if (pressedElement == element)
            {
                buttonColor = new RaylibColor(
                    (byte)(buttonColor.r * 0.8f),
                    (byte)(buttonColor.g * 0.8f),
                    (byte)(buttonColor.b * 0.8f),
                    buttonColor.a
                );
            }

            // Draw button
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, buttonColor);

            // Draw border
            Raylib.DrawRectangleLines((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, BlackColor);

            // Draw text
            if (!string.IsNullOrEmpty(element.Text))
            {
                int fontSize = element.Name.StartsWith("Tab") ? 16 : 14;
                int textWidth = Raylib.MeasureText(element.Text, fontSize);
                int textX = (int)(element.Bounds.x + (element.Bounds.width - textWidth) / 2);
                int textY = (int)(element.Bounds.y + (element.Bounds.height - fontSize) / 2);

                RaylibColor textColor = element.Name == "CloseButton" ? WhiteColor :
                    (element.Name.StartsWith("Tab") ? WhiteColor : BlackColor);

                Raylib.DrawText(element.Text, textX, textY, fontSize, textColor);
            }
        }

        private void RenderSlider(RaylibUIElement element)
        {
            // Draw track
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, BlackColor);

            // Draw track border
            Raylib.DrawRectangleLines((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, GrayColor);

            // Calculate handle position
            float normalizedValue = (element.Value - element.MinValue) / (element.MaxValue - element.MinValue);
            int handleX = (int)(element.Bounds.x + normalizedValue * element.Bounds.width);
            int handleY = (int)(element.Bounds.y + element.Bounds.height / 2);
            int handleRadius = (int)(element.Bounds.height / 2) + 2;

            // Draw handle
            Raylib.DrawCircle(handleX, handleY, handleRadius, BlackColor);
            Raylib.DrawCircleLines(handleX, handleY, handleRadius, WhiteColor);

            // Draw value text
            string valueText = element.Value.ToString("F2");
            int textWidth = Raylib.MeasureText(valueText, 12);
            Raylib.DrawText(valueText, (int)(element.Bounds.x + element.Bounds.width + 10),
                (int)(element.Bounds.y + 4), 12, WhiteColor);
        }

        private void RenderLabel(RaylibUIElement element)
        {
            if (!string.IsNullOrEmpty(element.Text))
            {
                Raylib.DrawText(element.Text, (int)element.Bounds.x, (int)element.Bounds.y, 16, element.Color);
            }
        }

        public void Dispose()
        {
            Close();
        }
    }
}
