using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Kiva_MIDI
{
    /// <summary>
    /// WPF-based settings window that opens separately from the Raylib renderer
    /// Recreates the original SettingsWindow.xaml functionality
    /// </summary>
    public class RaylibSettingsWindow : Window
    {
        private Settings settings;
        private TabControl tabControl;
        
        // Tab panels
        private StackPanel visualPanel;
        private StackPanel audioPanel;
        private StackPanel miscPanel;
        private StackPanel advancedPanel;

        public RaylibSettingsWindow(Settings settings)
        {
            this.settings = settings;
            InitializeWindow();
            CreateUI();
            LoadSettings();
        }

        private void InitializeWindow()
        {
            Title = "Kiva MIDI Settings";
            Width = 608;
            Height = 400;
            WindowStyle = WindowStyle.None;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(Color.FromRgb(1, 121, 219)); // #0179db
            
            // Allow dragging by clicking anywhere on the window
            MouseLeftButtonDown += (s, e) => DragMove();
        }

        private void CreateUI()
        {
            var mainGrid = new Grid();
            Content = mainGrid;

            // Title bar
            CreateTitleBar(mainGrid);

            // Content area with tabs
            CreateContentArea(mainGrid);
        }

        private void CreateTitleBar(Grid mainGrid)
        {
            var titleBar = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0, 104, 201)), // #0068c9
                Height = 40,
                VerticalAlignment = VerticalAlignment.Top
            };

            // Drop shadow effect
            titleBar.Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                BlurRadius = 5,
                ShadowDepth = 0
            };

            var titleGrid = new Grid();
            titleBar.Child = titleGrid;

            // Tab buttons
            var tabPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Left
            };

            CreateTabButton(tabPanel, "Visual", 0);
            CreateTabButton(tabPanel, "Audio", 1);
            CreateTabButton(tabPanel, "Misc", 2);
            CreateTabButton(tabPanel, "Advanced", 3);

            titleGrid.Children.Add(tabPanel);

            // Close button
            var closeButton = new Button
            {
                Content = "×",
                Width = 20,
                Height = 20,
                Margin = new Thickness(3),
                HorizontalAlignment = HorizontalAlignment.Right,
                Background = new SolidColorBrush(Colors.Red),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0),
                FontSize = 14,
                FontWeight = FontWeights.Bold
            };
            closeButton.Click += (s, e) => Close();

            titleGrid.Children.Add(closeButton);
            mainGrid.Children.Add(titleBar);
        }

        private void CreateTabButton(StackPanel parent, string text, int tabIndex)
        {
            var button = new Button
            {
                Content = text,
                Width = 87,
                Height = 40,
                Background = new SolidColorBrush(Colors.Transparent),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0),
                FontSize = 14
            };

            button.Click += (s, e) => SwitchToTab(tabIndex);
            parent.Children.Add(button);
        }

        private void CreateContentArea(Grid mainGrid)
        {
            // Content area below title bar
            var contentBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(1, 121, 219)), // #0179db
                Margin = new Thickness(0, 40, 0, 0)
            };

            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Padding = new Thickness(20)
            };

            // Create tab control (invisible, we'll manage tabs manually)
            tabControl = new TabControl
            {
                Background = new SolidColorBrush(Colors.Transparent),
                BorderThickness = new Thickness(0)
            };

            // Create tab panels
            CreateVisualTab();
            CreateAudioTab();
            CreateMiscTab();
            CreateAdvancedTab();

            scrollViewer.Content = tabControl;
            contentBorder.Child = scrollViewer;
            mainGrid.Children.Add(contentBorder);

            // Show first tab by default
            SwitchToTab(0);
        }

        private void CreateVisualTab()
        {
            visualPanel = new StackPanel { Margin = new Thickness(10) };
            
            AddSectionHeader(visualPanel, "Visual Settings");
            AddCheckBox(visualPanel, "Show Keyboard", settings.General.KeyboardStyle != KeyboardStyle.None);
            AddCheckBox(visualPanel, "Show Info Panel", true);
            AddSlider(visualPanel, "Note Speed", 1.0, 0.1, 5.0);
            
            var visualTab = new TabItem
            {
                Header = "Visual",
                Content = visualPanel,
                Visibility = Visibility.Visible
            };
            
            tabControl.Items.Add(visualTab);
        }

        private void CreateAudioTab()
        {
            audioPanel = new StackPanel { Margin = new Thickness(10) };
            
            AddSectionHeader(audioPanel, "Audio Settings");
            AddComboBox(audioPanel, "Audio Engine", new[] { "KDMAPI", "WinMM", "PreRender" }, 0);
            AddSlider(audioPanel, "Volume", 1.0, 0.0, 1.0);
            
            var audioTab = new TabItem
            {
                Header = "Audio",
                Content = audioPanel,
                Visibility = Visibility.Collapsed
            };
            
            tabControl.Items.Add(audioTab);
        }

        private void CreateMiscTab()
        {
            miscPanel = new StackPanel { Margin = new Thickness(10) };
            
            AddSectionHeader(miscPanel, "Miscellaneous Settings");
            AddCheckBox(miscPanel, "Always on Top", false);
            AddCheckBox(miscPanel, "Auto-play on Load", true);
            
            var miscTab = new TabItem
            {
                Header = "Misc",
                Content = miscPanel,
                Visibility = Visibility.Collapsed
            };
            
            tabControl.Items.Add(miscTab);
        }

        private void CreateAdvancedTab()
        {
            advancedPanel = new StackPanel { Margin = new Thickness(10) };
            
            AddSectionHeader(advancedPanel, "Advanced Settings");
            AddTextBox(advancedPanel, "Buffer Size", "1024");
            AddSlider(advancedPanel, "Render Distance", 5.0, 1.0, 20.0);
            
            var advancedTab = new TabItem
            {
                Header = "Advanced",
                Content = advancedPanel,
                Visibility = Visibility.Collapsed
            };
            
            tabControl.Items.Add(advancedTab);
        }

        private void SwitchToTab(int tabIndex)
        {
            for (int i = 0; i < tabControl.Items.Count; i++)
            {
                var tab = (TabItem)tabControl.Items[i];
                tab.Visibility = i == tabIndex ? Visibility.Visible : Visibility.Collapsed;
            }
            
            tabControl.SelectedIndex = tabIndex;
        }

        private void AddSectionHeader(StackPanel parent, string text)
        {
            var header = new TextBlock
            {
                Text = text,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 10, 0, 10)
            };
            parent.Children.Add(header);
        }

        private void AddCheckBox(StackPanel parent, string text, bool isChecked)
        {
            var checkBox = new CheckBox
            {
                Content = text,
                IsChecked = isChecked,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 5, 0, 5),
                FontSize = 14
            };
            parent.Children.Add(checkBox);
        }

        private void AddSlider(StackPanel parent, string text, double value, double min, double max)
        {
            var label = new TextBlock
            {
                Text = $"{text}: {value:F2}",
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 5, 0, 2),
                FontSize = 14
            };
            parent.Children.Add(label);

            var slider = new Slider
            {
                Minimum = min,
                Maximum = max,
                Value = value,
                Margin = new Thickness(0, 0, 0, 10),
                Width = 300,
                HorizontalAlignment = HorizontalAlignment.Left
            };
            
            slider.ValueChanged += (s, e) =>
            {
                label.Text = $"{text}: {e.NewValue:F2}";
            };
            
            parent.Children.Add(slider);
        }

        private void AddComboBox(StackPanel parent, string text, string[] items, int selectedIndex)
        {
            var label = new TextBlock
            {
                Text = text,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 5, 0, 2),
                FontSize = 14
            };
            parent.Children.Add(label);

            var comboBox = new ComboBox
            {
                Width = 200,
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 0, 0, 10),
                SelectedIndex = selectedIndex
            };
            
            foreach (var item in items)
            {
                comboBox.Items.Add(item);
            }
            
            parent.Children.Add(comboBox);
        }

        private void AddTextBox(StackPanel parent, string text, string value)
        {
            var label = new TextBlock
            {
                Text = text,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 5, 0, 2),
                FontSize = 14
            };
            parent.Children.Add(label);

            var textBox = new TextBox
            {
                Text = value,
                Width = 200,
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(5)
            };
            parent.Children.Add(textBox);
        }

        private void LoadSettings()
        {
            // Load current settings into the UI
            // This would be expanded to load actual settings values
        }

        public new void ShowDialog()
        {
            base.ShowDialog();
        }
    }
}
