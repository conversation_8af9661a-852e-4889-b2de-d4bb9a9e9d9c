using System;
using System.Collections.Generic;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Comprehensive UI manager for Raylib-based interface
    /// </summary>
    public class RaylibUIManager
    {
        private int screenWidth;
        private int screenHeight;
        private List<UIElement> uiElements;
        private UIElement hoveredElement;
        private UIElement pressedElement;
        
        // UI Colors
        private readonly RaylibColor BarColor = new RaylibColor(0, 104, 201, 255); // Blue
        private readonly RaylibColor BarDarkColor = new RaylibColor(0, 83, 161, 255); // Darker blue
        private readonly RaylibColor BlackColor = new RaylibColor(0, 0, 0, 255);
        private readonly RaylibColor WhiteColor = new RaylibColor(255, 255, 255, 255);
        private readonly RaylibColor GrayColor = new RaylibColor(128, 128, 128, 255);
        private readonly RaylibColor LightGrayColor = new RaylibColor(200, 200, 200, 255);
        
        // UI Layout
        private const int TopBarHeight = 60;
        private const int ButtonSize = 40;
        private const int SliderHeight = 20;
        private const int Margin = 10;
        
        public RaylibUIManager()
        {
            uiElements = new List<UIElement>();
        }
        
        public void SetScreenSize(int width, int height)
        {
            screenWidth = width;
            screenHeight = height;
            CreateUIElements();
        }
        
        private void CreateUIElements()
        {
            uiElements.Clear();
            
            // Top bar background
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Background,
                Bounds = new Rectangle(0, 0, screenWidth, TopBarHeight),
                Color = BarColor,
                Name = "TopBar"
            });
            
            int x = Margin;
            
            // Open button
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(x, (TopBarHeight - ButtonSize) / 2, ButtonSize, ButtonSize),
                Color = BarDarkColor,
                Name = "OpenButton",
                Text = "O",
                Action = () => OnOpenButtonClick()
            });
            x += ButtonSize + Margin;
            
            // Play button
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(x, (TopBarHeight - ButtonSize) / 2, ButtonSize, ButtonSize),
                Color = BarDarkColor,
                Name = "PlayButton",
                Text = "▶",
                Action = () => OnPlayButtonClick()
            });
            x += ButtonSize + Margin;
            
            // Pause button
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(x, (TopBarHeight - ButtonSize) / 2, ButtonSize, ButtonSize),
                Color = BarDarkColor,
                Name = "PauseButton",
                Text = "⏸",
                Action = () => OnPauseButtonClick()
            });
            x += ButtonSize + Margin;
            
            // Settings button
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Button,
                Bounds = new Rectangle(x, (TopBarHeight - ButtonSize) / 2, ButtonSize, ButtonSize),
                Color = BarDarkColor,
                Name = "SettingsButton",
                Text = "⚙",
                Action = () => OnSettingsButtonClick()
            });
            x += ButtonSize + Margin * 2;
            
            // Speed label
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Label,
                Bounds = new Rectangle(x, (TopBarHeight - 20) / 2, 60, 20),
                Color = WhiteColor,
                Name = "SpeedLabel",
                Text = "Speed:"
            });
            x += 70;
            
            // Speed slider
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Slider,
                Bounds = new Rectangle(x, (TopBarHeight - SliderHeight) / 2, 200, SliderHeight),
                Color = BlackColor,
                Name = "SpeedSlider",
                Value = 0.5f, // 50% = normal speed
                MinValue = 0.01f,
                MaxValue = 10.0f,
                Action = () => OnSpeedSliderChange()
            });
            x += 220;
            
            // Size label
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Label,
                Bounds = new Rectangle(x, (TopBarHeight - 20) / 2, 50, 20),
                Color = WhiteColor,
                Name = "SizeLabel",
                Text = "Size:"
            });
            x += 60;
            
            // Size slider (note speed)
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Slider,
                Bounds = new Rectangle(x, (TopBarHeight - SliderHeight) / 2, 200, SliderHeight),
                Color = BlackColor,
                Name = "SizeSlider",
                Value = 0.5f, // 50% = normal size
                MinValue = 0.01f,
                MaxValue = 10.0f,
                Action = () => OnSizeSliderChange()
            });
            
            // Time/seek slider (bottom of top bar)
            uiElements.Add(new UIElement
            {
                Type = UIElementType.Slider,
                Bounds = new Rectangle(Margin, TopBarHeight - 25, screenWidth - 2 * Margin, 15),
                Color = BlackColor,
                Name = "TimeSlider",
                Value = 0.0f,
                MinValue = 0.0f,
                MaxValue = 1.0f,
                Action = () => OnTimeSliderChange()
            });
        }
        
        public void Update()
        {
            HandleInput();
        }
        
        private void HandleInput()
        {
            Vector2 mousePos = Raylib.GetMousePosition();
            bool mousePressed = Raylib.IsMouseButtonPressed(MOUSE_BUTTON_LEFT);
            bool mouseDown = Raylib.IsMouseButtonDown(MOUSE_BUTTON_LEFT);
            bool mouseReleased = Raylib.IsMouseButtonReleased(MOUSE_BUTTON_LEFT);
            
            // Find hovered element
            UIElement newHovered = null;
            foreach (var element in uiElements)
            {
                if (element.Type != UIElementType.Background && 
                    IsPointInRectangle(mousePos, element.Bounds))
                {
                    newHovered = element;
                    break;
                }
            }
            
            // Handle hover changes
            if (hoveredElement != newHovered)
            {
                hoveredElement = newHovered;
            }
            
            // Handle mouse press
            if (mousePressed && hoveredElement != null)
            {
                pressedElement = hoveredElement;
            }
            
            // Handle mouse release
            if (mouseReleased && pressedElement != null)
            {
                if (pressedElement == hoveredElement)
                {
                    // Click occurred
                    pressedElement.Action?.Invoke();
                }
                pressedElement = null;
            }
            
            // Handle slider dragging
            if (mouseDown && pressedElement?.Type == UIElementType.Slider)
            {
                UpdateSliderValue(pressedElement, mousePos);
            }
            
            // Handle keyboard shortcuts
            HandleKeyboardShortcuts();
        }
        
        private void HandleKeyboardShortcuts()
        {
            bool ctrlPressed = Raylib.IsKeyDown(KEY_LEFT_CONTROL) || Raylib.IsKeyDown(KEY_RIGHT_CONTROL);
            
            if (ctrlPressed)
            {
                if (Raylib.IsKeyPressed(KEY_UP))
                {
                    AdjustSpeed(0.1f);
                }
                else if (Raylib.IsKeyPressed(KEY_DOWN))
                {
                    AdjustSpeed(-0.1f);
                }
            }
        }
        
        private void UpdateSliderValue(UIElement slider, Vector2 mousePos)
        {
            float relativeX = (mousePos.X - slider.Bounds.x) / slider.Bounds.width;
            relativeX = Math.Max(0, Math.Min(1, relativeX));
            
            slider.Value = slider.MinValue + relativeX * (slider.MaxValue - slider.MinValue);
            slider.Action?.Invoke();
        }
        
        private bool IsPointInRectangle(Vector2 point, Rectangle rect)
        {
            return point.X >= rect.x && point.X <= rect.x + rect.width &&
                   point.Y >= rect.y && point.Y <= rect.y + rect.height;
        }
        
        // Event handlers (to be implemented by the main window)
        public event Action OnOpenButtonClick = delegate { };
        public event Action OnPlayButtonClick = delegate { };
        public event Action OnPauseButtonClick = delegate { };
        public event Action OnSettingsButtonClick = delegate { };
        public event Action OnSpeedSliderChange = delegate { };
        public event Action OnSizeSliderChange = delegate { };
        public event Action OnTimeSliderChange = delegate { };
        
        private void AdjustSpeed(float delta)
        {
            var speedSlider = GetElement("SpeedSlider");
            if (speedSlider != null)
            {
                speedSlider.Value = Math.Max(speedSlider.MinValue, 
                    Math.Min(speedSlider.MaxValue, speedSlider.Value + delta));
                OnSpeedSliderChange?.Invoke();
            }
        }
        
        public UIElement GetElement(string name)
        {
            return uiElements.Find(e => e.Name == name);
        }
        
        public void SetSliderValue(string name, float value)
        {
            var element = GetElement(name);
            if (element?.Type == UIElementType.Slider)
            {
                element.Value = Math.Max(element.MinValue, Math.Min(element.MaxValue, value));
            }
        }
        
        public float GetSliderValue(string name)
        {
            var element = GetElement(name);
            return element?.Type == UIElementType.Slider ? element.Value : 0f;
        }

        public void Render()
        {
            foreach (var element in uiElements)
            {
                RenderElement(element);
            }
        }

        private void RenderElement(UIElement element)
        {
            switch (element.Type)
            {
                case UIElementType.Background:
                    RenderBackground(element);
                    break;
                case UIElementType.Button:
                    RenderButton(element);
                    break;
                case UIElementType.Slider:
                    RenderSlider(element);
                    break;
                case UIElementType.Label:
                    RenderLabel(element);
                    break;
            }
        }

        private void RenderBackground(UIElement element)
        {
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, element.Color);
        }

        private void RenderButton(UIElement element)
        {
            RaylibColor buttonColor = element.Color;

            // Hover effect
            if (hoveredElement == element)
            {
                buttonColor = new RaylibColor(
                    (byte)Math.Min(255, buttonColor.r * 1.2f),
                    (byte)Math.Min(255, buttonColor.g * 1.2f),
                    (byte)Math.Min(255, buttonColor.b * 1.2f),
                    buttonColor.a
                );
            }

            // Press effect
            if (pressedElement == element)
            {
                buttonColor = new RaylibColor(
                    (byte)(buttonColor.r * 0.8f),
                    (byte)(buttonColor.g * 0.8f),
                    (byte)(buttonColor.b * 0.8f),
                    buttonColor.a
                );
            }

            // Draw button background
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, buttonColor);

            // Draw button border
            Raylib.DrawRectangleLines((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, BlackColor);

            // Draw button text
            if (!string.IsNullOrEmpty(element.Text))
            {
                int textWidth = Raylib.MeasureText(element.Text, 20);
                int textX = (int)(element.Bounds.x + (element.Bounds.width - textWidth) / 2);
                int textY = (int)(element.Bounds.y + (element.Bounds.height - 20) / 2);
                Raylib.DrawText(element.Text, textX, textY, 20, WhiteColor);
            }
        }

        private void RenderSlider(UIElement element)
        {
            // Draw slider track (black stretching texture)
            Raylib.DrawRectangle((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, BlackColor);

            // Draw slider track border
            Raylib.DrawRectangleLines((int)element.Bounds.x, (int)element.Bounds.y,
                (int)element.Bounds.width, (int)element.Bounds.height, GrayColor);

            // Calculate handle position
            float normalizedValue = (element.Value - element.MinValue) / (element.MaxValue - element.MinValue);
            int handleX = (int)(element.Bounds.x + normalizedValue * element.Bounds.width);
            int handleY = (int)(element.Bounds.y + element.Bounds.height / 2);
            int handleRadius = (int)(element.Bounds.height / 2) + 2;

            // Draw slider handle (black circle)
            Raylib.DrawCircle(handleX, handleY, handleRadius, BlackColor);

            // Draw handle border
            Raylib.DrawCircleLines(handleX, handleY, handleRadius, WhiteColor);

            // Hover effect on handle
            if (hoveredElement == element)
            {
                Raylib.DrawCircleLines(handleX, handleY, handleRadius + 2, LightGrayColor);
            }
        }

        private void RenderLabel(UIElement element)
        {
            if (!string.IsNullOrEmpty(element.Text))
            {
                Raylib.DrawText(element.Text, (int)element.Bounds.x, (int)element.Bounds.y, 16, element.Color);
            }
        }
    }

    public enum UIElementType
    {
        Background,
        Button,
        Slider,
        Label
    }

    public class UIElement
    {
        public UIElementType Type { get; set; }
        public Rectangle Bounds { get; set; }
        public RaylibColor Color { get; set; }
        public string Name { get; set; }
        public string Text { get; set; }
        public float Value { get; set; }
        public float MinValue { get; set; }
        public float MaxValue { get; set; }
        public Action Action { get; set; }
    }
}
